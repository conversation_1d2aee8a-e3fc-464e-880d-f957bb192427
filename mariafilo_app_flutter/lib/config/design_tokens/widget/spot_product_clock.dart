import 'package:flutter/material.dart';
import 'package:soma_ui/soma_ui.dart';
import 'package:soma_core/soma_core.dart';
import 'package:maria_filo_app_flutter/widgets/clock_time.dart';

class MariaFiloSpotProductClock extends StatefulWidget {
  final String? title;
  final String? subtitle;
  final String? coupon;
  final String? buttonText;
  final Color? backgroundColor;
  final Color? textColor;
  final VoidCallback? onTapButton;
  final Duration? remainingTime;
  final BannerNameGA4? bannerNameGA4;
  final String? callToAction;
  final String? filterCategoryOrCluster;
  final String? orderBy;
  final String? rulesButtonText;
  final String? rulesDescription;

  const MariaFiloSpotProductClock({
    super.key,
    this.title,
    this.subtitle,
    this.coupon,
    this.buttonText,
    this.backgroundColor,
    this.textColor,
    this.onTapButton,
    this.remainingTime,
    this.bannerNameGA4,
    this.callToAction,
    this.filterCategoryOrCluster,
    this.orderBy,
    this.rulesButtonText,
    this.rulesDescription,
  });

  @override
  State<MariaFiloSpotProductClock> createState() =>
      _MariaFiloSpotProductClockState();
}

class _MariaFiloSpotProductClockState extends State<MariaFiloSpotProductClock>
    with
        DesignTokensStateMixin,
        CouponPromotionMixin,
        AnalyticsEventDispatcherStateMixin,
        AppRoutesStateMixin,
        SomaCoreStateMixin {
  // Implementação obrigatória do CouponPromotionMixin
  @override
  String? get coupon => widget.coupon;

  @override
  String get snackbarTextForCouponApplied => 'Cupom adicionado';

  @override
  String get snackbarTextForEmptyBag =>
      'Ops! você precisa ter 1 ou mais produtos na sua sacola';

  Search get getSearch => Search(
        title: widget.title,
        filterCategoryOrCluster: widget.filterCategoryOrCluster,
        orderBy: widget.orderBy != null
            ? Search.getOrderByString(widget.orderBy!)
            : null,
      );

  void _logPromotionEvents({bool isViewPromotion = false}) {
    final inheritedMetadata = AnalyticsMetadataProvider.metadataOf(context);
    String? screenName = inheritedMetadata?['screen_name'].toLowerCase();
    if (isViewPromotion) {
      try {
        dispatchViewPromotionEvent(
          context,
          screenClass: 'reloginho',
          screenName: screenName,
          promotionName: widget.bannerNameGA4?.promotionName,
          creativeName: widget.bannerNameGA4?.creativeName,
        );
      } catch (e) {
        debugPrint("Error on dispatchViewPromotionEvent: $e");
      }
    } else {
      try {
        dispatchSelectPromotionEvent(
          screenClass: 'reloginho',
          screenName: screenName,
          promotionName: widget.bannerNameGA4?.promotionName,
          creativeName: widget.bannerNameGA4?.creativeName,
          creativeSlot: 'Banner sem imagem',
          promotionId: "reloginho-$screenName",
        );
      } catch (e) {
        debugPrint("Error on dispatchSelectPromotionEvent: $e");
      }
    }
  }

  Widget _buttomWidget() {
    return SMButton.secondary(
      size: ButtonSize.extraSmall,
      onPressed: () async {
        _logPromotionEvents();
        if (hasCoupon) {
          await onTapCoupon();
        } else {
          if (widget.onTapButton != null) {
            widget.onTapButton!();
          } else {
            dispatchSelectContentEvent(
                'combos:${widget.title}:${widget.callToAction}');
            intelligentSearchController.clearSearch();
            navigateToNamed(searchResultRoute, arguments: getSearch);
          }
        }
      },
      style: SMButtonStyle(
        padding: const EdgeInsets.fromLTRB(5, 10, 5, 10),
        foregroundColor: widget.textColor,
        backgroundColor: Colors.transparent,
        border: isButtonSelected
            ? DashedBorder(
                color: widget.textColor ?? Colors.transparent,
              )
            : Border.all(
                color: widget.textColor ?? Colors.transparent,
                width: 1,
              ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            hasCoupon ? getClockButtonText() : 'VEJA MAIS PRODUTOS',
            style: typography.typeStyles.bodyCaption.copyWith(
                color: widget.textColor,
                fontWeight: FontWeight.bold,
                fontSize: tokens.typography.fontSizes.xxus,
                height: 1),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final remainingTime = widget.remainingTime ?? Duration.zero;
    final backgroundColor = widget.backgroundColor ?? tokens.colors.brand.pure2;
    final textColor = widget.textColor ?? tokens.colors.typography.pure1;
    return Container(
        width: double.infinity,
        color: backgroundColor,
        padding: EdgeInsets.symmetric(
          horizontal: tokens.spacingInset.md,
          vertical: 0,
        ),
        child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                flex: 1,
                child: ClockTime(
                  remainingTime: remainingTime,
                  textColor: textColor,
                  hasIcon: false,
                ),
              ),
              SizedBox(width: tokens.spacingInline.md),
              Expanded(
                flex: 1,
                child: _buttomWidget(),
              ),
            ]));
  }
}
