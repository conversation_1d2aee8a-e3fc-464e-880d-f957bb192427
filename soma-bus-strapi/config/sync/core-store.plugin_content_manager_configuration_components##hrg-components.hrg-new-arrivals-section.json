{"key": "plugin_content_manager_configuration_components::hrg-components.hrg-new-arrivals-section", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "title", "defaultSortBy": "title", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": false, "sortable": false}}, "title": {"edit": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "O título principal do componente", "placeholder": "Ex: <PERSON><PERSON><PERSON> as novid<PERSON>", "visible": true, "editable": true}, "list": {"label": "title", "searchable": true, "sortable": true}}, "media": {"edit": {"label": "Mí<PERSON>", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "media", "searchable": false, "sortable": false}}, "navigate_to": {"edit": {"label": "Navegação dinâmica", "description": "A navegação dinâmica que será realizada ao clicar no botão de \"Ir para o catálogo\", ou pela forma de navegação automática", "placeholder": "Ex: plp/productclusterids/862", "visible": true, "editable": true}, "list": {"label": "navigate_to", "searchable": true, "sortable": true}}, "show_anchor_button": {"edit": {"label": "<PERSON>e exibir bot<PERSON>?", "description": "Define se o botão de \"Voltar para o topo\" deve ser exibido", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "show_anchor_button", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "title", "media", "navigate_to"], "edit": [[{"name": "title", "size": 6}, {"name": "media", "size": 6}], [{"name": "navigate_to", "size": 6}, {"name": "show_anchor_button", "size": 4}]]}, "uid": "hrg-components.hrg-new-arrivals-section", "isComponent": true}, "type": "object", "environment": null, "tag": null}