import type { Schema, Attribute } from '@strapi/strapi';

export interface AdminPermission extends Schema.CollectionType {
  collectionName: 'admin_permissions';
  info: {
    name: 'Permission';
    description: '';
    singularName: 'permission';
    pluralName: 'permissions';
    displayName: 'Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    actionParameters: Attribute.JSON & Attribute.DefaultTo<{}>;
    subject: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    properties: Attribute.JSON & Attribute.DefaultTo<{}>;
    conditions: Attribute.JSON & Attribute.DefaultTo<[]>;
    role: Attribute.Relation<'admin::permission', 'manyToOne', 'admin::role'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminUser extends Schema.CollectionType {
  collectionName: 'admin_users';
  info: {
    name: 'User';
    description: '';
    singularName: 'user';
    pluralName: 'users';
    displayName: 'User';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    firstname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    username: Attribute.String;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.Private &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    resetPasswordToken: Attribute.String & Attribute.Private;
    registrationToken: Attribute.String & Attribute.Private;
    isActive: Attribute.Boolean &
      Attribute.Private &
      Attribute.DefaultTo<false>;
    roles: Attribute.Relation<'admin::user', 'manyToMany', 'admin::role'> &
      Attribute.Private;
    blocked: Attribute.Boolean & Attribute.Private & Attribute.DefaultTo<false>;
    preferedLanguage: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface AdminRole extends Schema.CollectionType {
  collectionName: 'admin_roles';
  info: {
    name: 'Role';
    description: '';
    singularName: 'role';
    pluralName: 'roles';
    displayName: 'Role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    code: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String;
    users: Attribute.Relation<'admin::role', 'manyToMany', 'admin::user'>;
    permissions: Attribute.Relation<
      'admin::role',
      'oneToMany',
      'admin::permission'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface AdminApiToken extends Schema.CollectionType {
  collectionName: 'strapi_api_tokens';
  info: {
    name: 'Api Token';
    singularName: 'api-token';
    pluralName: 'api-tokens';
    displayName: 'Api Token';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    type: Attribute.Enumeration<['read-only', 'full-access', 'custom']> &
      Attribute.Required &
      Attribute.DefaultTo<'read-only'>;
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastUsedAt: Attribute.DateTime;
    permissions: Attribute.Relation<
      'admin::api-token',
      'oneToMany',
      'admin::api-token-permission'
    >;
    expiresAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminApiTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_api_token_permissions';
  info: {
    name: 'API Token Permission';
    description: '';
    singularName: 'api-token-permission';
    pluralName: 'api-token-permissions';
    displayName: 'API Token Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    token: Attribute.Relation<
      'admin::api-token-permission',
      'manyToOne',
      'admin::api-token'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminTransferToken extends Schema.CollectionType {
  collectionName: 'strapi_transfer_tokens';
  info: {
    name: 'Transfer Token';
    singularName: 'transfer-token';
    pluralName: 'transfer-tokens';
    displayName: 'Transfer Token';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastUsedAt: Attribute.DateTime;
    permissions: Attribute.Relation<
      'admin::transfer-token',
      'oneToMany',
      'admin::transfer-token-permission'
    >;
    expiresAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminTransferTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_transfer_token_permissions';
  info: {
    name: 'Transfer Token Permission';
    description: '';
    singularName: 'transfer-token-permission';
    pluralName: 'transfer-token-permissions';
    displayName: 'Transfer Token Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    token: Attribute.Relation<
      'admin::transfer-token-permission',
      'manyToOne',
      'admin::transfer-token'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUploadFile extends Schema.CollectionType {
  collectionName: 'files';
  info: {
    singularName: 'file';
    pluralName: 'files';
    displayName: 'File';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    alternativeText: Attribute.String;
    caption: Attribute.String;
    width: Attribute.Integer;
    height: Attribute.Integer;
    formats: Attribute.JSON;
    hash: Attribute.String & Attribute.Required;
    ext: Attribute.String;
    mime: Attribute.String & Attribute.Required;
    size: Attribute.Decimal & Attribute.Required;
    url: Attribute.String & Attribute.Required;
    previewUrl: Attribute.String;
    provider: Attribute.String & Attribute.Required;
    provider_metadata: Attribute.JSON;
    related: Attribute.Relation<'plugin::upload.file', 'morphToMany'>;
    folder: Attribute.Relation<
      'plugin::upload.file',
      'manyToOne',
      'plugin::upload.folder'
    > &
      Attribute.Private;
    folderPath: Attribute.String &
      Attribute.Required &
      Attribute.Private &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUploadFolder extends Schema.CollectionType {
  collectionName: 'upload_folders';
  info: {
    singularName: 'folder';
    pluralName: 'folders';
    displayName: 'Folder';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    pathId: Attribute.Integer & Attribute.Required & Attribute.Unique;
    parent: Attribute.Relation<
      'plugin::upload.folder',
      'manyToOne',
      'plugin::upload.folder'
    >;
    children: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.folder'
    >;
    files: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.file'
    >;
    path: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginContentReleasesRelease extends Schema.CollectionType {
  collectionName: 'strapi_releases';
  info: {
    singularName: 'release';
    pluralName: 'releases';
    displayName: 'Release';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    releasedAt: Attribute.DateTime;
    scheduledAt: Attribute.DateTime;
    timezone: Attribute.String;
    status: Attribute.Enumeration<
      ['ready', 'blocked', 'failed', 'done', 'empty']
    > &
      Attribute.Required;
    actions: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToMany',
      'plugin::content-releases.release-action'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginContentReleasesReleaseAction
  extends Schema.CollectionType {
  collectionName: 'strapi_release_actions';
  info: {
    singularName: 'release-action';
    pluralName: 'release-actions';
    displayName: 'Release Action';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    type: Attribute.Enumeration<['publish', 'unpublish']> & Attribute.Required;
    entry: Attribute.Relation<
      'plugin::content-releases.release-action',
      'morphToOne'
    >;
    contentType: Attribute.String & Attribute.Required;
    locale: Attribute.String;
    release: Attribute.Relation<
      'plugin::content-releases.release-action',
      'manyToOne',
      'plugin::content-releases.release'
    >;
    isEntryValid: Attribute.Boolean;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::content-releases.release-action',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::content-releases.release-action',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsPermission
  extends Schema.CollectionType {
  collectionName: 'up_permissions';
  info: {
    name: 'permission';
    description: '';
    singularName: 'permission';
    pluralName: 'permissions';
    displayName: 'Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String & Attribute.Required;
    role: Attribute.Relation<
      'plugin::users-permissions.permission',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsRole extends Schema.CollectionType {
  collectionName: 'up_roles';
  info: {
    name: 'role';
    description: '';
    singularName: 'role';
    pluralName: 'roles';
    displayName: 'Role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    description: Attribute.String;
    type: Attribute.String & Attribute.Unique;
    permissions: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.permission'
    >;
    users: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.user'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsUser extends Schema.CollectionType {
  collectionName: 'up_users';
  info: {
    name: 'user';
    description: '';
    singularName: 'user';
    pluralName: 'users';
    displayName: 'User';
  };
  options: {
    draftAndPublish: false;
    timestamps: true;
  };
  attributes: {
    username: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    provider: Attribute.String;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    resetPasswordToken: Attribute.String & Attribute.Private;
    confirmationToken: Attribute.String & Attribute.Private;
    confirmed: Attribute.Boolean & Attribute.DefaultTo<false>;
    blocked: Attribute.Boolean & Attribute.DefaultTo<false>;
    role: Attribute.Relation<
      'plugin::users-permissions.user',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginPublisherAction extends Schema.CollectionType {
  collectionName: 'actions';
  info: {
    singularName: 'action';
    pluralName: 'actions';
    displayName: 'actions';
  };
  options: {
    draftAndPublish: false;
    comment: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    executeAt: Attribute.DateTime;
    mode: Attribute.String;
    entityId: Attribute.Integer;
    entitySlug: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::publisher.action',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::publisher.action',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginI18NLocale extends Schema.CollectionType {
  collectionName: 'i18n_locale';
  info: {
    singularName: 'locale';
    pluralName: 'locales';
    collectionName: 'locales';
    displayName: 'Locale';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetMinMax<
        {
          min: 1;
          max: 50;
        },
        number
      >;
    code: Attribute.String & Attribute.Unique;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAiVirtualAssistantsHeringAiVirtualAssistantsHering
  extends Schema.CollectionType {
  collectionName: 'ai_virtual_assistant_hering';
  info: {
    singularName: 'ai-virtual-assistants-hering';
    pluralName: 'ai-virtual-assistant-hering';
    displayName: 'Assistente Virtual IA';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    welcoming_message: Attribute.String & Attribute.Required;
    asking_suggestions: Attribute.DynamicZone<
      ['ai-assistant.ai-asking-suggestion']
    > &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::ai-virtual-assistants-hering.ai-virtual-assistants-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::ai-virtual-assistants-hering.ai-virtual-assistants-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiBagsAnimaleBagsAnimale extends Schema.CollectionType {
  collectionName: 'bag_animale';
  info: {
    singularName: 'bags-animale';
    pluralName: 'bag-animale';
    displayName: 'Sacola';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['highlight.highlight-list', 'spot-product.spot-product']
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::bags-animale.bags-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::bags-animale.bags-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiBagsCrisbarrosBagsCrisbarros extends Schema.CollectionType {
  collectionName: 'bag_crisbarros';
  info: {
    singularName: 'bags-crisbarros';
    pluralName: 'bag-crisbarros';
    displayName: 'Sacola';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String;
    components: Attribute.DynamicZone<
      ['spot-product.spot-product', 'highlight.highlight-optional-media']
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::bags-crisbarros.bags-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::bags-crisbarros.bags-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiBagsFarmBagsFarm extends Schema.CollectionType {
  collectionName: 'bag_farm';
  info: {
    singularName: 'bags-farm';
    pluralName: 'bag-farm';
    displayName: 'Sacola';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'highlight.highlight-list',
        'spot-product.spot-product',
        'cashback.message-cashback'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::bags-farm.bags-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::bags-farm.bags-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiBagsHeringBagsHering extends Schema.CollectionType {
  collectionName: 'bag_hering';
  info: {
    singularName: 'bags-hering';
    pluralName: 'bag-hering';
    displayName: 'Sacola';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['highlight.highlight-list', 'spot-product.spot-product']
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::bags-hering.bags-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::bags-hering.bags-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiBagsMariafiloBagsMariafilo extends Schema.CollectionType {
  collectionName: 'bag_mariafilo';
  info: {
    singularName: 'bags-mariafilo';
    pluralName: 'bag-mariafilo';
    displayName: 'Sacola';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['highlight.highlight-list', 'spot-product.spot-product']
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::bags-mariafilo.bags-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::bags-mariafilo.bags-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiBagsNvBagsNv extends Schema.CollectionType {
  collectionName: 'bag_nv';
  info: {
    singularName: 'bags-nv';
    pluralName: 'bag-nv';
    displayName: 'Sacola';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['highlight.highlight-list', 'spot-product.spot-product']
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::bags-nv.bags-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::bags-nv.bags-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiBagsOffpremiumBagsOffpremium extends Schema.CollectionType {
  collectionName: 'bag_offpremium';
  info: {
    singularName: 'bags-offpremium';
    pluralName: 'bag-offpremium';
    displayName: 'Sacola';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['highlight.highlight-list', 'spot-product.spot-product']
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::bags-offpremium.bags-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::bags-offpremium.bags-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCallCentersHeringCallCentersHering
  extends Schema.CollectionType {
  collectionName: 'call_center_hering';
  info: {
    singularName: 'call-centers-hering';
    pluralName: 'call-center-hering';
    displayName: 'Central de atendimento';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<['call-center.faq', 'call-center.cards']>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::call-centers-hering.call-centers-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::call-centers-hering.call-centers-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiClockPromotionAnimalesClockPromotionAnimales
  extends Schema.CollectionType {
  collectionName: 'clock_promotion_animale';
  info: {
    singularName: 'clock-promotion-animales';
    pluralName: 'clock-promotion-animale';
    displayName: 'Reloginhos';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['clock-promotion.clock-header-animale']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::clock-promotion-animales.clock-promotion-animales',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::clock-promotion-animales.clock-promotion-animales',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiClockPromotionFarmsClockPromotionFarms
  extends Schema.CollectionType {
  collectionName: 'clock_promotion_farm';
  info: {
    singularName: 'clock-promotion-farms';
    pluralName: 'clock-promotion-farm';
    displayName: 'Reloginhos';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'clock-promotion.clock-header-farm',
        'clock-promotion.clock-product-farm'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::clock-promotion-farms.clock-promotion-farms',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::clock-promotion-farms.clock-promotion-farms',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiClockPromotionHeringsClockPromotionHerings
  extends Schema.CollectionType {
  collectionName: 'clock_promotion_hering';
  info: {
    singularName: 'clock-promotion-herings';
    pluralName: 'clock-promotion-hering';
    displayName: 'Reloginhos';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'clock-promotion.clock-header-hering',
        'clock-promotion.clock-product-hering',
        'clusters-components.general-clock-cluster',
        'clusters-components.product-clock-cluster'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::clock-promotion-herings.clock-promotion-herings',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::clock-promotion-herings.clock-promotion-herings',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiClockPromotionMariafilosClockPromotionMariafilos
  extends Schema.CollectionType {
  collectionName: 'clock_promotion_mariafilo';
  info: {
    singularName: 'clock-promotion-mariafilos';
    pluralName: 'clock-promotion-mariafilo';
    displayName: 'Reloginhos';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['clock-promotion.clock-header-maria-filo']
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::clock-promotion-mariafilos.clock-promotion-mariafilos',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::clock-promotion-mariafilos.clock-promotion-mariafilos',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiClockPromotionNvsClockPromotionNvs
  extends Schema.CollectionType {
  collectionName: 'clock_promotion_nv';
  info: {
    singularName: 'clock-promotion-nvs';
    pluralName: 'clock-promotion-nv';
    displayName: 'Reloginhos';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['clock-promotion.clock-header-nv']>;
    name: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::clock-promotion-nvs.clock-promotion-nvs',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::clock-promotion-nvs.clock-promotion-nvs',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiClockPromotionOffpremiumsClockPromotionOffpremiums
  extends Schema.CollectionType {
  collectionName: 'clock_promotion_offpremium';
  info: {
    singularName: 'clock-promotion-offpremiums';
    pluralName: 'clock-promotion-offpremium';
    displayName: 'Reloginhos';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['clock-promotion.clock-header-off']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::clock-promotion-offpremiums.clock-promotion-offpremiums',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::clock-promotion-offpremiums.clock-promotion-offpremiums',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiComboDePromocoessHeringComboDePromocoessHering
  extends Schema.CollectionType {
  collectionName: 'combo_de_promocoes_hering';
  info: {
    singularName: 'combo-de-promocoess-hering';
    pluralName: 'combo-de-promocoes-hering';
    displayName: 'Combo de promo\u00E7\u00F5es';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    nome_interno: Attribute.String;
    components: Attribute.DynamicZone<
      ['combo-promotion.carousel-combo-promotion', 'combo-promotion.combo']
    >;
    page_filters: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::combo-de-promocoess-hering.combo-de-promocoess-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::combo-de-promocoess-hering.combo-de-promocoess-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCouponsAnimaleCouponsAnimale extends Schema.CollectionType {
  collectionName: 'coupon_animale';
  info: {
    singularName: 'coupons-animale';
    pluralName: 'coupon-animale';
    displayName: 'Cupons';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['cupons.cupom']>;
    name: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::coupons-animale.coupons-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::coupons-animale.coupons-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCreatedSuitcasesHeringCreatedSuitcasesHering
  extends Schema.CollectionType {
  collectionName: 'created_suitcase_hering';
  info: {
    singularName: 'created-suitcases-hering';
    pluralName: 'created-suitcase-hering';
    displayName: 'Created Suitcase';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'created-suitcase-empty.created-suitcase-empty',
        'category-banner.category-banner',
        'showcase-list.show-case-list'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::created-suitcases-hering.created-suitcases-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::created-suitcases-hering.created-suitcases-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiDeeplinksAnimaleDeeplinksAnimale
  extends Schema.CollectionType {
  collectionName: 'deeplink_animale';
  info: {
    singularName: 'deeplinks-animale';
    pluralName: 'deeplink-animale';
    displayName: 'Deeplinks';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    nome_interno: Attribute.String & Attribute.Required;
    invalid_deeplink: Attribute.Component<'deeplink.invalid-deeplink', true>;
    valid_deeplinks: Attribute.Component<'deeplink.deeplinks', true>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::deeplinks-animale.deeplinks-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::deeplinks-animale.deeplinks-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiDeeplinksCrisbarrosDeeplinksCrisbarros
  extends Schema.CollectionType {
  collectionName: 'deeplink_crisbarros';
  info: {
    singularName: 'deeplinks-crisbarros';
    pluralName: 'deeplink-crisbarros';
    displayName: 'Deeplinks';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    nome_interno: Attribute.String & Attribute.Required;
    deeplinks: Attribute.Component<'deeplink.deeplinks', true>;
    invalid_deeplink: Attribute.Component<'deeplink.invalid-deeplink', true>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::deeplinks-crisbarros.deeplinks-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::deeplinks-crisbarros.deeplinks-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiDeeplinksFarmDeeplinksFarm extends Schema.CollectionType {
  collectionName: 'deeplink_farm';
  info: {
    singularName: 'deeplinks-farm';
    pluralName: 'deeplink-farm';
    displayName: 'Deeplinks';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    nome_interno: Attribute.String & Attribute.Required;
    invalid_deeplink: Attribute.Component<'deeplink.invalid-deeplink', true>;
    valid_deeplinks: Attribute.Component<'deeplink.deeplinks', true>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::deeplinks-farm.deeplinks-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::deeplinks-farm.deeplinks-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiDeeplinksHeringDeeplinksHering
  extends Schema.CollectionType {
  collectionName: 'deeplink_hering';
  info: {
    singularName: 'deeplinks-hering';
    pluralName: 'deeplink-hering';
    displayName: 'Deeplinks';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    nome_interno: Attribute.String & Attribute.Required;
    invalid_deeplink: Attribute.Component<'deeplink.invalid-deeplink', true>;
    valid_deeplinks: Attribute.Component<'deeplink.deeplinks', true>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::deeplinks-hering.deeplinks-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::deeplinks-hering.deeplinks-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiDeeplinksMariafiloDeeplinksMariafilo
  extends Schema.CollectionType {
  collectionName: 'deeplink_mariafilo';
  info: {
    singularName: 'deeplinks-mariafilo';
    pluralName: 'deeplink-mariafilo';
    displayName: 'Deeplink';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    nome_interno: Attribute.String & Attribute.Required;
    invalid_deeplink: Attribute.Component<'deeplink.invalid-deeplink', true>;
    valid_deeplinks: Attribute.Component<'deeplink.deeplinks', true>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::deeplinks-mariafilo.deeplinks-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::deeplinks-mariafilo.deeplinks-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiDeeplinksNvDeeplinksNv extends Schema.CollectionType {
  collectionName: 'deeplink_nv';
  info: {
    singularName: 'deeplinks-nv';
    pluralName: 'deeplink-nv';
    displayName: 'Deeplinks';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    nome_interno: Attribute.String & Attribute.Required;
    invalid_deeplink: Attribute.Component<'deeplink.invalid-deeplink', true>;
    valid_deeplinks: Attribute.Component<'deeplink.deeplinks', true>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::deeplinks-nv.deeplinks-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::deeplinks-nv.deeplinks-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiDeeplinksOffpremiumDeeplinksOffpremium
  extends Schema.CollectionType {
  collectionName: 'deeplink_offpremium';
  info: {
    singularName: 'deeplinks-offpremium';
    pluralName: 'deeplink-offpremium';
    displayName: 'Deeplink';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    nome_interno: Attribute.String & Attribute.Required;
    invalid_deeplink: Attribute.Component<'deeplink.invalid-deeplink', true>;
    valid_deeplinks: Attribute.Component<'deeplink.deeplinks', true>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::deeplinks-offpremium.deeplinks-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::deeplinks-offpremium.deeplinks-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEmptyBagsAnimaleEmptyBagsAnimale
  extends Schema.CollectionType {
  collectionName: 'empty_bag_animale';
  info: {
    singularName: 'empty-bags-animale';
    pluralName: 'empty-bag-animale';
    displayName: 'Sacola Vazia';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['highlight.highlight-list', 'spot-product.spot-product']
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::empty-bags-animale.empty-bags-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::empty-bags-animale.empty-bags-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEmptyBagsCrisbarrosEmptyBagsCrisbarros
  extends Schema.CollectionType {
  collectionName: 'empty_bag_crisbarros';
  info: {
    singularName: 'empty-bags-crisbarros';
    pluralName: 'empty-bag-crisbarros';
    displayName: 'Sacola Vazia';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<
      ['spot-product.spot-product', 'highlight.highlight-optional-media']
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::empty-bags-crisbarros.empty-bags-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::empty-bags-crisbarros.empty-bags-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEmptyBagsFarmEmptyBagsFarm extends Schema.CollectionType {
  collectionName: 'empty_bag_farm';
  info: {
    singularName: 'empty-bags-farm';
    pluralName: 'empty-bag-farm';
    displayName: 'Sacola Vazia';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['highlight.highlight-list', 'spot-product.spot-product']
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::empty-bags-farm.empty-bags-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::empty-bags-farm.empty-bags-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEmptyBagsHeringEmptyBagsHering
  extends Schema.CollectionType {
  collectionName: 'empty_bag_hering';
  info: {
    singularName: 'empty-bags-hering';
    pluralName: 'empty-bag-hering';
    displayName: 'Sacola Vazia';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['highlight.highlight-list', 'spot-product.spot-product']
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::empty-bags-hering.empty-bags-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::empty-bags-hering.empty-bags-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEmptyBagsMariafiloEmptyBagsMariafilo
  extends Schema.CollectionType {
  collectionName: 'empty_bag_mariafilo';
  info: {
    singularName: 'empty-bags-mariafilo';
    pluralName: 'empty-bag-mariafilo';
    displayName: 'Sacola Vazia';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['highlight.highlight-list', 'spot-product.spot-product']
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::empty-bags-mariafilo.empty-bags-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::empty-bags-mariafilo.empty-bags-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEmptyBagsNvEmptyBagsNv extends Schema.CollectionType {
  collectionName: 'empty_bag_nv';
  info: {
    singularName: 'empty-bags-nv';
    pluralName: 'empty-bag-nv';
    displayName: 'Sacola Vazia';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['highlight.highlight-list', 'spot-product.spot-product']
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::empty-bags-nv.empty-bags-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::empty-bags-nv.empty-bags-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEmptyBagsOffpremiumEmptyBagsOffpremium
  extends Schema.CollectionType {
  collectionName: 'empty_bag_offpremium';
  info: {
    singularName: 'empty-bags-offpremium';
    pluralName: 'empty-bag-offpremium';
    displayName: 'Sacola Vazia';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['highlight.highlight-list', 'spot-product.spot-product']
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::empty-bags-offpremium.empty-bags-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::empty-bags-offpremium.empty-bags-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEmptyWishlistsAnimaleEmptyWishlistsAnimale
  extends Schema.CollectionType {
  collectionName: 'empty_wishlist_animale';
  info: {
    singularName: 'empty-wishlists-animale';
    pluralName: 'empty-wishlist-animale';
    displayName: 'Wishlist Vazia';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['spot-product.spot-product']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::empty-wishlists-animale.empty-wishlists-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::empty-wishlists-animale.empty-wishlists-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEmptyWishlistsCrisbarrosEmptyWishlistsCrisbarros
  extends Schema.CollectionType {
  collectionName: 'empty_wishlist_crisbarros';
  info: {
    singularName: 'empty-wishlists-crisbarros';
    pluralName: 'empty-wishlist-crisbarros';
    displayName: 'Wishlist Vazia';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<
      ['spot-product.spot-product', 'highlight.highlight-list']
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::empty-wishlists-crisbarros.empty-wishlists-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::empty-wishlists-crisbarros.empty-wishlists-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEmptyWishlistsHeringEmptyWishlistsHering
  extends Schema.CollectionType {
  collectionName: 'empty_wishlist_hering';
  info: {
    singularName: 'empty-wishlists-hering';
    pluralName: 'empty-wishlist-hering';
    displayName: 'Wishlist Vazia';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['spot-product.spot-product']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::empty-wishlists-hering.empty-wishlists-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::empty-wishlists-hering.empty-wishlists-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEmptyWishlistsMariafiloEmptyWishlistsMariafilo
  extends Schema.CollectionType {
  collectionName: 'empty_wishlist_mariafilo';
  info: {
    singularName: 'empty-wishlists-mariafilo';
    pluralName: 'empty-wishlist-mariafilo';
    displayName: 'Wishlist Vazia';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['spot-product.spot-product']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::empty-wishlists-mariafilo.empty-wishlists-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::empty-wishlists-mariafilo.empty-wishlists-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEmptyWishlistsNvEmptyWishlistsNv
  extends Schema.CollectionType {
  collectionName: 'empty_wishlist_nv';
  info: {
    singularName: 'empty-wishlists-nv';
    pluralName: 'empty-wishlist-nv';
    displayName: 'Wishlist Vazia';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['personal-showcase.personal-showcase', 'spot-product.spot-product']
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::empty-wishlists-nv.empty-wishlists-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::empty-wishlists-nv.empty-wishlists-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEmptyWishlistsOffpremiumEmptyWishlistsOffpremium
  extends Schema.CollectionType {
  collectionName: 'empty_wishlist_offpremium';
  info: {
    singularName: 'empty-wishlists-offpremium';
    pluralName: 'empty-wishlist-offpremium';
    displayName: 'Wishlist Vazia';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['spot-product.spot-product']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::empty-wishlists-offpremium.empty-wishlists-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::empty-wishlists-offpremium.empty-wishlists-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEmptysNotificationsCentersNvEmptysNotificationsCentersNv
  extends Schema.CollectionType {
  collectionName: 'empty_notifications_center_nv';
  info: {
    singularName: 'emptys-notifications-centers-nv';
    pluralName: 'empty-notification-center-nv';
    displayName: 'Central de Notifica\u00E7\u00F5es Vazia';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['notification-center.redirect-section', 'showcase-list.show-case-list']
    > &
      Attribute.Required;
    name: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::emptys-notifications-centers-nv.emptys-notifications-centers-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::emptys-notifications-centers-nv.emptys-notifications-centers-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEmptysSearchsNvEmptysSearchsNv
  extends Schema.CollectionType {
  collectionName: 'empty_search_nv';
  info: {
    singularName: 'emptys-searchs-nv';
    pluralName: 'empty-search-nv';
    displayName: 'Busca Vazia';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['showcase-banner.showcase-banner', 'categories-mosaic.categories-mosaic']
    >;
    name: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::emptys-searchs-nv.emptys-searchs-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::emptys-searchs-nv.emptys-searchs-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiFidelitiesNvFidelitiesNv extends Schema.CollectionType {
  collectionName: 'fidelity_nv';
  info: {
    singularName: 'fidelities-nv';
    pluralName: 'fidelity-nv';
    displayName: 'Fidelidade';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'fidelity.invite',
        'fidelity.club-example',
        'fidelity.join-club',
        'fidelity.redirect-button',
        'fidelity.rate-club',
        'fidelity.benefits',
        'fidelity.faq',
        'fidelity.how-work',
        'fidelity.level-description',
        'fidelity.user-info',
        'showcase-list.show-case-list',
        'fidelity.points-for-register'
      ]
    >;
    has_fidelity: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
    name: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::fidelities-nv.fidelities-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::fidelities-nv.fidelities-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiFullLookPagesFarmsFullLookPagesFarms
  extends Schema.CollectionType {
  collectionName: 'full_look_page_farm';
  info: {
    singularName: 'full-look-pages-farms';
    pluralName: 'full-look-page-farm';
    displayName: 'P\u00E1gina Complete o look';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'full-look-carousel.full-look-carousel-item',
        'media-kit-banner.media-kit-banner'
      ]
    >;
    title: Attribute.String;
    subtitle: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::full-look-pages-farms.full-look-pages-farms',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::full-look-pages-farms.full-look-pages-farms',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiFullLookPagesHeringsFullLookPagesHerings
  extends Schema.CollectionType {
  collectionName: 'full_look_page_hering';
  info: {
    singularName: 'full-look-pages-herings';
    pluralName: 'full-look-page-hering';
    displayName: 'P\u00E1gina Complete o look';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'media-kit-banner.media-kit-banner',
        'full-look-carousel.full-look-carousel-item'
      ]
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::full-look-pages-herings.full-look-pages-herings',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::full-look-pages-herings.full-look-pages-herings',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiGeneralHeaderPdcEtcsFarmGeneralHeaderPdcEtcsFarm
  extends Schema.CollectionType {
  collectionName: 'general_header_pdc_etc_farm';
  info: {
    singularName: 'general-header-pdc-etcs-farm';
    pluralName: 'general-header-pdc-etc-farm';
    displayName: 'Header Geral PDC ETC';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Header: Attribute.Component<'pdc-header-etc.pdc-header-etc'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::general-header-pdc-etcs-farm.general-header-pdc-etcs-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::general-header-pdc-etcs-farm.general-header-pdc-etcs-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiHomesAnimaleHomesAnimale extends Schema.CollectionType {
  collectionName: 'home_animale';
  info: {
    singularName: 'homes-animale';
    pluralName: 'home-animale';
    displayName: 'Home';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'carousel-list.carousel-list',
        'categories-mosaic.categories-mosaic',
        'category-banner.category-banner',
        'spot-product-clock.spot-product-clock',
        'spot-product.spot-product',
        'showcase-list.show-case-list',
        'video-gallery.video-gallery',
        'coupon-banner.coupon-banner',
        'stories.stories',
        'my-interests-banner.my-interests-banner',
        'clock.clock-animale',
        'clock-promotion.clock-banner-animale'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::homes-animale.homes-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::homes-animale.homes-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiHomesCrisbarrosHomesCrisbarros
  extends Schema.CollectionType {
  collectionName: 'home_crisbarros';
  info: {
    singularName: 'homes-crisbarros';
    pluralName: 'home-crisbarros';
    displayName: 'Home';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<
      [
        'categories-mosaic.categories-mosaic',
        'spot-product.spot-product',
        'media-list.media-list',
        'personal-showcase.personal-showcase',
        'mosaic.products-mosaic',
        'video-banner.video-banner',
        'content-area.content-area-with-sign',
        'showcase-banner-list.show-case-banner-carrousel'
      ]
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::homes-crisbarros.homes-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::homes-crisbarros.homes-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiHomesEtcHomesEtc extends Schema.CollectionType {
  collectionName: 'home_etc';
  info: {
    singularName: 'homes-etc';
    pluralName: 'home-etc';
    displayName: 'Home ETC';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'carousel-list.carousel-list',
        'grid-category-listing.grid-category-listing',
        'brand-slogan-lockup.brand-slogan',
        'badge-discount.badge-discount',
        'banner-content-text.banner-content-text',
        'banner-content-text.banner-content-text-double-media',
        'etc.products-grid-filter',
        'etc.home-composition',
        'etc.image-content'
      ]
    >;
    nome_interno: Attribute.String;
    header_etc: Attribute.Component<'etc.header-etc'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::homes-etc.homes-etc',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::homes-etc.homes-etc',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiHomesFarmHomesFarm extends Schema.CollectionType {
  collectionName: 'home_farm';
  info: {
    singularName: 'homes-farm';
    pluralName: 'home-farm';
    displayName: 'Home';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'carousel-list.carousel-list',
        'categories-mosaic.categories-mosaic',
        'category-banner.category-banner',
        'clock.clock-farm',
        'spot-product-clock.spot-product-clock',
        'coupon-banner.coupon-banner',
        'spot-product.spot-product',
        'showcase-list.show-case-list',
        'video-gallery.video-gallery',
        'stories.stories',
        'clusters-components.category-banner-cluster',
        'full-look-carousel.full-look-carousel'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::homes-farm.homes-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::homes-farm.homes-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiHomesHeringHomesHering extends Schema.CollectionType {
  collectionName: 'home_hering';
  info: {
    singularName: 'homes-hering';
    pluralName: 'home-hering';
    displayName: 'Home';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'carousel-list.carousel-list',
        'categories-mosaic.categories-mosaic',
        'category-banner.category-banner',
        'clock.clock-hering',
        'spot-product-clock.spot-product-clock',
        'spot-product.spot-product',
        'showcase-list.show-case-list',
        'video-gallery.video-gallery',
        'coupon-banner.coupon-banner',
        'stories.stories',
        'my-interests-banner.my-interests-banner',
        'smart-recommendation.insider-recommended-showcase',
        'clusters-components.coupon-banner-cluster',
        'full-look-carousel.full-look-carousel'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::homes-hering.homes-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::homes-hering.homes-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiHomesMariafiloHomesMariafilo extends Schema.CollectionType {
  collectionName: 'home_mariafilo';
  info: {
    singularName: 'homes-mariafilo';
    pluralName: 'home-mariafilo';
    displayName: 'Home';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'carousel-list.carousel-list',
        'categories-mosaic.categories-mosaic',
        'category-banner.category-banner',
        'clock.clock-maria-filo',
        'spot-product-clock.spot-product-clock',
        'spot-product.spot-product',
        'showcase-list.show-case-list',
        'video-gallery.video-gallery',
        'coupon-banner.coupon-banner',
        'stories.stories'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::homes-mariafilo.homes-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::homes-mariafilo.homes-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiHomesNvHomesNv extends Schema.CollectionType {
  collectionName: 'home_nv';
  info: {
    singularName: 'homes-nv';
    pluralName: 'home-nv';
    displayName: 'Home';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'personal-showcase.personal-showcase',
        'showcase-banner.showcase-banner',
        'content-area.content-area',
        'banner-media.banner-media',
        'mosaic.categories-mosaic',
        'mosaic.products-mosaic',
        'video-banner.video-banner',
        'my-interests-banner.my-interesses-with-header',
        'showcase-list.show-case-list',
        'clock-promotion.clock-banner-nv',
        'media-list.media-list'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::homes-nv.homes-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::homes-nv.homes-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiHomesOffpremiumHomesOffpremium
  extends Schema.CollectionType {
  collectionName: 'home_offpremium';
  info: {
    singularName: 'homes-offpremium';
    pluralName: 'home-offpremium';
    displayName: 'Home';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'carousel-list.carousel-list',
        'categories-mosaic.categories-mosaic',
        'category-banner.category-banner',
        'clock.clock-off',
        'spot-product-clock.spot-product-clock',
        'spot-product.spot-product',
        'showcase-list.show-case-list',
        'video-gallery.video-gallery',
        'coupon-banner.coupon-banner',
        'stories.stories'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::homes-offpremium.homes-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::homes-offpremium.homes-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiHrgHomesHeringHrgHomesHering extends Schema.CollectionType {
  collectionName: 'hrg_home_hering';
  info: {
    singularName: 'hrg-homes-hering';
    pluralName: 'hrg-home-hering';
    displayName: 'HRG Home';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<
      [
        'hrg-components.hrg-spot-product',
        'carousel-banner.carousel-banner',
        'hrg-components.hrg-categories-banners',
        'hrg-components.hrg-new-arrivals-section',
        'hrg-components.hrg-dynamic-navigation-banners'
      ]
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::hrg-homes-hering.hrg-homes-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::hrg-homes-hering.hrg-homes-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiLiveTransmissionsMariafiloLiveTransmissionsMariafilo
  extends Schema.CollectionType {
  collectionName: 'live_transmission_mariafilo';
  info: {
    singularName: 'live-transmissions-mariafilo';
    pluralName: 'live-transmission-mariafilo';
    displayName: 'Transmiss\u00E3o Live';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    title: Attribute.String;
    live_id: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<['carousel-list.carousel-list']>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::live-transmissions-mariafilo.live-transmissions-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::live-transmissions-mariafilo.live-transmissions-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiLoginBackgroundsCrisbarrosLoginBackgroundsCrisbarros
  extends Schema.CollectionType {
  collectionName: 'login_background_crisbarros';
  info: {
    singularName: 'login-backgrounds-crisbarros';
    pluralName: 'login-background-crisbarros';
    displayName: 'Login Background';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    inverse: Attribute.Boolean & Attribute.Required & Attribute.DefaultTo<true>;
    background: Attribute.Media & Attribute.Required;
    complement_image: Attribute.Media & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::login-backgrounds-crisbarros.login-backgrounds-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::login-backgrounds-crisbarros.login-backgrounds-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiLoginBackgroundsFarmLoginBackgroundsFarm
  extends Schema.CollectionType {
  collectionName: 'login_background_farm';
  info: {
    singularName: 'login-backgrounds-farm';
    pluralName: 'login-background-farm';
    displayName: 'Login Background';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    inverse: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
    background: Attribute.Media & Attribute.Required;
    complement_image: Attribute.Media & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::login-backgrounds-farm.login-backgrounds-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::login-backgrounds-farm.login-backgrounds-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMediaKitsEtcFarmMediaKitsEtcFarm
  extends Schema.CollectionType {
  collectionName: 'media_kit_etc_farm';
  info: {
    singularName: 'media-kits-etc-farm';
    pluralName: 'media-kit-etc-farm';
    displayName: 'Media Kit ETC';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    category_or_cluster: Attribute.String & Attribute.Required;
    name: Attribute.String & Attribute.Required;
    header: Attribute.Component<'pdc-header-etc.pdc-header-etc'>;
    components: Attribute.DynamicZone<
      ['media-kit-product-card.media-kit-product-card']
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::media-kits-etc-farm.media-kits-etc-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::media-kits-etc-farm.media-kits-etc-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMediaKitsFarmMediaKitsFarm extends Schema.CollectionType {
  collectionName: 'media_kit_farm';
  info: {
    singularName: 'media-kits-farm';
    pluralName: 'media-kit-farm';
    displayName: 'Media Kit';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required & Attribute.Private;
    category_or_cluster: Attribute.Text & Attribute.Required;
    components: Attribute.DynamicZone<
      [
        'media-kit-banner.media-kit-banner',
        'media-kit-product-card.media-kit-product-card'
      ]
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::media-kits-farm.media-kits-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::media-kits-farm.media-kits-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMediaKitsHeringMediaKitsHering
  extends Schema.CollectionType {
  collectionName: 'media_kit_hering';
  info: {
    singularName: 'media-kits-hering';
    pluralName: 'media-kit-hering';
    displayName: 'Media Kit';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required & Attribute.Private;
    category_or_cluster: Attribute.Text & Attribute.Required;
    components: Attribute.DynamicZone<
      [
        'media-kit-banner.media-kit-banner',
        'media-kit-full-look.media-kit-full-look-banner'
      ]
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::media-kits-hering.media-kits-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::media-kits-hering.media-kits-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMediaKitsMariafiloMediaKitsMariafilo
  extends Schema.CollectionType {
  collectionName: 'media_kit_mariafilo';
  info: {
    singularName: 'media-kits-mariafilo';
    pluralName: 'media-kit-mariafilo';
    displayName: 'Media Kit';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    category_or_cluster: Attribute.Text & Attribute.Required;
    components: Attribute.DynamicZone<['media-kit-banner.media-kit-banner']>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::media-kits-mariafilo.media-kits-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::media-kits-mariafilo.media-kits-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMediaKitsNvMediaKitsNv extends Schema.CollectionType {
  collectionName: 'media_kit_nv';
  info: {
    singularName: 'media-kits-nv';
    pluralName: 'media-kit-nv';
    displayName: 'Media Kit';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required & Attribute.Private;
    category_or_cluster: Attribute.Text & Attribute.Required;
    components: Attribute.DynamicZone<
      [
        'media-kit-banner.media-kit-banner-nv',
        'media-kit-banner-list.media-kit-banner-list-nv',
        'media-kit-categories-mosaic.media-kit-categories-mosaic-nv'
      ]
    >;
    grid_density: Attribute.Integer &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
          max: 2;
        },
        number
      >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::media-kits-nv.media-kits-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::media-kits-nv.media-kits-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenuCollectionsFarmMenuCollectionsFarm
  extends Schema.CollectionType {
  collectionName: 'menu_collection_farm';
  info: {
    singularName: 'menu-collections-farm';
    pluralName: 'menu-collection-farm';
    displayName: 'Cole\u00E7\u00F5es Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Name: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<['menu-collections.menu-collections']>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menu-collections-farm.menu-collections-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menu-collections-farm.menu-collections-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenuCollectionsFarmEtcMenuCollectionsFarmEtc
  extends Schema.CollectionType {
  collectionName: 'menu_collection_farm_etc';
  info: {
    singularName: 'menu-collections-farm-etc';
    pluralName: 'menu-collection-farm-etc';
    displayName: 'Cole\u00E7\u00F5es Menu ETC';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Name: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<['menu-collections.menu-collections']>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menu-collections-farm-etc.menu-collections-farm-etc',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menu-collections-farm-etc.menu-collections-farm-etc',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenuContentsAnimaleMenuContentsAnimale
  extends Schema.CollectionType {
  collectionName: 'menu_content_animale';
  info: {
    singularName: 'menu-contents-animale';
    pluralName: 'menu-content-animale';
    displayName: 'Conte\u00FAdo do Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'accordion-menu.accordion-menu',
        'list-links.list-links',
        'my-interests-card.my-interests-card',
        'category-banner.category-banner',
        'showcase-list.show-case-list',
        'categories-mosaic.categories-mosaic',
        'highlight.highlight-list',
        'spot-product.spot-product'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menu-contents-animale.menu-contents-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menu-contents-animale.menu-contents-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenuContentsCrisbarrosMenuContentsCrisbarros
  extends Schema.CollectionType {
  collectionName: 'menu_content_crisbarros';
  info: {
    singularName: 'menu-contents-crisbarros';
    pluralName: 'menu-content-crisbarros';
    displayName: 'Conte\u00FAdo do Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<
      [
        'accordion-menu.accordion-menu',
        'my-interests-card.my-interests-card',
        'category-banner.category-banner',
        'showcase-list.show-case-list',
        'categories-mosaic.categories-mosaic',
        'highlight.highlight-list',
        'spot-product.spot-product'
      ]
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menu-contents-crisbarros.menu-contents-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menu-contents-crisbarros.menu-contents-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenuContentsFarmMenuContentsFarm
  extends Schema.CollectionType {
  collectionName: 'menu_content_farm';
  info: {
    singularName: 'menu-contents-farm';
    pluralName: 'menu-content-farm';
    displayName: 'Conte\u00FAdo do Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'accordion-menu.accordion-menu',
        'list-links.list-links',
        'category-banner.category-banner',
        'showcase-list.show-case-list',
        'categories-mosaic.categories-mosaic',
        'highlight.highlight-list',
        'spot-product.spot-product',
        'carousel-banner.carousel-banner',
        'clock.clock-farm',
        'accordion-menu.accordion-menu-etc',
        'accordion-menu.content-media-menu'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menu-contents-farm.menu-contents-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menu-contents-farm.menu-contents-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenuContentsFarmEtcMenuContentsFarmEtc
  extends Schema.CollectionType {
  collectionName: 'menu_content_farm_etc';
  info: {
    singularName: 'menu-contents-farm-etc';
    pluralName: 'menu-content-farm-etc';
    displayName: 'Conte\u00FAdo do Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<
      [
        'list-links.list-links',
        'accordion-menu.accordion-menu-etc',
        'accordion-menu.content-media-menu'
      ]
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menu-contents-farm-etc.menu-contents-farm-etc',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menu-contents-farm-etc.menu-contents-farm-etc',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenuContentsHeringMenuContentsHering
  extends Schema.CollectionType {
  collectionName: 'menu_content_hering';
  info: {
    singularName: 'menu-contents-hering';
    pluralName: 'menu-content-hering';
    displayName: 'Conte\u00FAdo do Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'accordion-menu.accordion-menu',
        'list-links.list-links',
        'my-interests-card.my-interests-card',
        'category-banner.category-banner',
        'showcase-list.show-case-list',
        'categories-mosaic.categories-mosaic',
        'highlight.highlight-list',
        'spot-product.spot-product'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menu-contents-hering.menu-contents-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menu-contents-hering.menu-contents-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenuContentsMariafiloMenuContentsMariafilo
  extends Schema.CollectionType {
  collectionName: 'menu_content_mariafilo';
  info: {
    singularName: 'menu-contents-mariafilo';
    pluralName: 'menu-content-mariafilo';
    displayName: 'Conte\u00FAdo do Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'accordion-menu.accordion-menu',
        'list-links.list-links',
        'category-banner.category-banner',
        'showcase-list.show-case-list',
        'categories-mosaic.categories-mosaic',
        'highlight.highlight-list',
        'spot-product.spot-product'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menu-contents-mariafilo.menu-contents-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menu-contents-mariafilo.menu-contents-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenuContentsNvMenuContentsNv extends Schema.CollectionType {
  collectionName: 'menu_content_nv';
  info: {
    singularName: 'menu-contents-nv';
    pluralName: 'menu-content-nv';
    displayName: 'Conte\u00FAdo do Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'accordion-menu.accordion-menu',
        'category-banner.category-banner',
        'list-links.list-links',
        'my-interests-card.my-interests-card',
        'carousel-banner.carousel-banner',
        'sign-up-login.sign-up-login',
        'mosaic.categories-mosaic'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menu-contents-nv.menu-contents-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menu-contents-nv.menu-contents-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenuContentsOffpremiumMenuContentsOffpremium
  extends Schema.CollectionType {
  collectionName: 'menu_content_offpremium';
  info: {
    singularName: 'menu-contents-offpremium';
    pluralName: 'menu-content-offpremium';
    displayName: 'Conte\u00FAdo do Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'accordion-menu.accordion-menu',
        'list-links.list-links',
        'category-banner.category-banner',
        'showcase-list.show-case-list',
        'categories-mosaic.categories-mosaic',
        'highlight.highlight-list',
        'spot-product.spot-product'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menu-contents-offpremium.menu-contents-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menu-contents-offpremium.menu-contents-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenusAnimaleMenusAnimale extends Schema.CollectionType {
  collectionName: 'menu_animale';
  info: {
    singularName: 'menus-animale';
    pluralName: 'menu-animale';
    displayName: 'Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['menu-tab.menu-tab-animale']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menus-animale.menus-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menus-animale.menus-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenusCrisbarrosMenusCrisbarros
  extends Schema.CollectionType {
  collectionName: 'menu_crisbarros';
  info: {
    singularName: 'menus-crisbarros';
    pluralName: 'menu-crisbarros';
    displayName: 'Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<['menu-tab.menu-tab-crisbarros']>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menus-crisbarros.menus-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menus-crisbarros.menus-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenusFarmMenusFarm extends Schema.CollectionType {
  collectionName: 'menu_farm';
  info: {
    singularName: 'menus-farm';
    pluralName: 'menu-farm';
    displayName: 'Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'menu-tab.menu-tab-farm',
        'menu-tab.menu-tab-farm-new',
        'list-links.list-links-icons',
        'accordion-menu.content-media-menu'
      ]
    > &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    header_image: Attribute.Media;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menus-farm.menus-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menus-farm.menus-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenusFarmEtcMenusFarmEtc extends Schema.CollectionType {
  collectionName: 'menu_farm_etc';
  info: {
    singularName: 'menus-farm-etc';
    pluralName: 'menu-farm-etc';
    displayName: 'Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<
      ['menu-tab.menu-tab-farm-etc', 'accordion-menu.content-media-menu']
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menus-farm-etc.menus-farm-etc',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menus-farm-etc.menus-farm-etc',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenusHeringMenusHering extends Schema.CollectionType {
  collectionName: 'menu_hering';
  info: {
    singularName: 'menus-hering';
    pluralName: 'menu-hering';
    displayName: 'Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['menu-tab.menu-tab-hering']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menus-hering.menus-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menus-hering.menus-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenusMariafiloMenusMariafilo extends Schema.CollectionType {
  collectionName: 'menu_mariafilo';
  info: {
    singularName: 'menus-mariafilo';
    pluralName: 'menu-mariafilo';
    displayName: 'Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['menu-tab.menu-tab-mariafilo']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menus-mariafilo.menus-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menus-mariafilo.menus-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenusNvMenusNv extends Schema.CollectionType {
  collectionName: 'menu_nv';
  info: {
    singularName: 'menus-nv';
    pluralName: 'menu-nv';
    displayName: 'Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['menu-tab.menu-tab-nv']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menus-nv.menus-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menus-nv.menus-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMenusOffpremiumMenusOffpremium
  extends Schema.CollectionType {
  collectionName: 'menu_offpremium';
  info: {
    singularName: 'menus-offpremium';
    pluralName: 'menu-offpremium';
    displayName: 'Menu';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['menu-tab.menu-tab-offpremium']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::menus-offpremium.menus-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::menus-offpremium.menus-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiNonVipFeedbacksNvNonVipFeedbacksNv
  extends Schema.CollectionType {
  collectionName: 'non_vip_feedback_nv';
  info: {
    singularName: 'non-vip-feedbacks-nv';
    pluralName: 'non-vip-feedback-nv';
    displayName: 'Feedback n\u00E3o vips';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    description: Attribute.Text;
    call_to_action: Attribute.String;
    navigate_to: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::non-vip-feedbacks-nv.non-vip-feedbacks-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::non-vip-feedbacks-nv.non-vip-feedbacks-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiNotificationsCentersAnimaleNotificationsCentersAnimale
  extends Schema.CollectionType {
  collectionName: 'notification_center_animale';
  info: {
    singularName: 'notifications-centers-animale';
    pluralName: 'notification-center-animale';
    displayName: 'Central de Notifica\u00E7\u00F5es';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'notification-center.message-banner',
        'notification-center.message',
        'notification-center.page-data'
      ]
    > &
      Attribute.Required;
    name: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::notifications-centers-animale.notifications-centers-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::notifications-centers-animale.notifications-centers-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiNotificationsCentersFarmNotificationsCentersFarm
  extends Schema.CollectionType {
  collectionName: 'notifications_center_farm';
  info: {
    singularName: 'notifications-centers-farm';
    pluralName: 'notification-center-farm';
    displayName: 'Central de Notifica\u00E7\u00F5es';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'notification-center.message-banner',
        'notification-center.message',
        'notification-center.page-data',
        'notification-center.expanded-notification-message'
      ]
    > &
      Attribute.Required;
    name: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::notifications-centers-farm.notifications-centers-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::notifications-centers-farm.notifications-centers-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiNotificationsCentersHeringNotificationsCentersHering
  extends Schema.CollectionType {
  collectionName: 'notifications_center_hering';
  info: {
    singularName: 'notifications-centers-hering';
    pluralName: 'notification-center-hering';
    displayName: 'Central de Notifica\u00E7\u00F5es';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'notification-center.message-banner',
        'notification-center.message',
        'notification-center.page-data',
        'clusters-components.message-banner-clusters',
        'clusters-components.message-clusters'
      ]
    > &
      Attribute.Required;
    name: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::notifications-centers-hering.notifications-centers-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::notifications-centers-hering.notifications-centers-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiNotificationsCentersNvNotificationsCentersNv
  extends Schema.CollectionType {
  collectionName: 'notifications_center_nv';
  info: {
    singularName: 'notifications-centers-nv';
    pluralName: 'notification-center-nv';
    displayName: 'Central de Notifica\u00E7\u00F5es';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'notification-center.message-banner',
        'notification-center.message',
        'notification-center.page-data'
      ]
    > &
      Attribute.Required;
    name: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::notifications-centers-nv.notifications-centers-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::notifications-centers-nv.notifications-centers-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiOnboardingsHeringOnboardingsHering
  extends Schema.CollectionType {
  collectionName: 'onboarding_hering';
  info: {
    singularName: 'onboardings-hering';
    pluralName: 'onboarding-hering';
    displayName: 'Onboarding';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'step.step',
        'search-step.search-step',
        'notification-step.notification-step',
        'tracking-step.tracking-step-default'
      ]
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::onboardings-hering.onboardings-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::onboardings-hering.onboardings-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiOnboardingsNvOnboardingsNv extends Schema.CollectionType {
  collectionName: 'onboarding_nv';
  info: {
    singularName: 'onboardings-nv';
    pluralName: 'onboarding-nv';
    displayName: 'Onboarding';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'search-step.search-step',
        'step.step',
        'notification-step.notification-step',
        'tracking-step.tracking-step'
      ]
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::onboardings-nv.onboardings-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::onboardings-nv.onboardings-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiOrderPlacedsCrisbarrosOrderPlacedsCrisbarros
  extends Schema.CollectionType {
  collectionName: 'order_placed_crisbarros';
  info: {
    singularName: 'order-placeds-crisbarros';
    pluralName: 'order-placed-crisbarros';
    displayName: 'Order Placed';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String;
    components: Attribute.DynamicZone<['showcase-list.show-case-list']>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::order-placeds-crisbarros.order-placeds-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::order-placeds-crisbarros.order-placeds-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiOrdersDetailsHeringOrdersDetailsHering
  extends Schema.CollectionType {
  collectionName: 'order_details_hering';
  info: {
    singularName: 'orders-details-hering';
    pluralName: 'order-details-hering';
    displayName: 'Order Details';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    warning_text: Attribute.Component<'order-details.order-details-warning-text'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::orders-details-hering.orders-details-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::orders-details-hering.orders-details-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPaymentsCrisbarrosPaymentsCrisbarros
  extends Schema.CollectionType {
  collectionName: 'payment_crisbarros';
  info: {
    singularName: 'payments-crisbarros';
    pluralName: 'payment-crisbarros';
    displayName: 'Payment';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    paymentWarning: Attribute.Component<'payment-warning.payment-warning'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::payments-crisbarros.payments-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::payments-crisbarros.payments-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPdpEtcsFarmPdpEtcsFarm extends Schema.CollectionType {
  collectionName: 'pdp_etc_farm';
  info: {
    singularName: 'pdp-etcs-farm';
    pluralName: 'pdp-etc-farm';
    displayName: 'PDP ETC';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<
      ['product-carousel-image.product-carousel-image']
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::pdp-etcs-farm.pdp-etcs-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::pdp-etcs-farm.pdp-etcs-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiProductPdpsAnimaleProductPdpsAnimale
  extends Schema.CollectionType {
  collectionName: 'product_pdp_animale';
  info: {
    singularName: 'product-pdps-animale';
    pluralName: 'product-pdp-animale';
    displayName: 'PDP de produto';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    spot_product: Attribute.Component<'spot-product.spot-product'>;
    highlight: Attribute.Component<'highlight.highlight-list'>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::product-pdps-animale.product-pdps-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::product-pdps-animale.product-pdps-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiProductPdpsCrisbarrosProductPdpsCrisbarros
  extends Schema.CollectionType {
  collectionName: 'product_pdp_crisbarros';
  info: {
    singularName: 'product-pdps-crisbarros';
    pluralName: 'product-pdp-crisbarros';
    displayName: 'PDP de produto';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    spot_product: Attribute.Component<'spot-product.spot-product'>;
    highlightOptional: Attribute.Component<'highlight.highlight-optional-media'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::product-pdps-crisbarros.product-pdps-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::product-pdps-crisbarros.product-pdps-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiProductPdpsFarmProductPdpsFarm
  extends Schema.CollectionType {
  collectionName: 'product_pdp_farm';
  info: {
    singularName: 'product-pdps-farm';
    pluralName: 'product-pdp-farm';
    displayName: 'PDP de produto';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    spot_product: Attribute.Component<'spot-product.spot-product'>;
    highlight: Attribute.Component<'highlight.highlight-list'>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    cashback: Attribute.Component<'cashback.message-cashback-pdp'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::product-pdps-farm.product-pdps-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::product-pdps-farm.product-pdps-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiProductPdpsHeringProductPdpsHering
  extends Schema.CollectionType {
  collectionName: 'product_pdp_hering';
  info: {
    singularName: 'product-pdps-hering';
    pluralName: 'product-pdp-hering';
    displayName: 'PDP de produto';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    spot_product: Attribute.Component<'spot-product.spot-product'>;
    highlight: Attribute.Component<'highlight.highlight-list'>;
    insider_showcase_2: Attribute.Component<'smart-recommendation.insider-recommended-showcase'>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    insider_showcase: Attribute.Component<'smart-recommendation.insider-recommended-showcase'>;
    stamp_collection: Attribute.Component<'product-stamp.sport-stamp-collection'>;
    combo_steps: Attribute.Component<'combo-promotion.combo-steps', true>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::product-pdps-hering.product-pdps-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::product-pdps-hering.product-pdps-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiProductPdpsMariafiloProductPdpsMariafilo
  extends Schema.CollectionType {
  collectionName: 'product_pdp_mariafilo';
  info: {
    singularName: 'product-pdps-mariafilo';
    pluralName: 'product-pdp-mariafilo';
    displayName: 'PDP de produto';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    spot_product: Attribute.Component<'spot-product.spot-product'>;
    highlight: Attribute.Component<'highlight.highlight-list'>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::product-pdps-mariafilo.product-pdps-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::product-pdps-mariafilo.product-pdps-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiProductPdpsNvProductPdpsNv extends Schema.CollectionType {
  collectionName: 'product_pdp_nv';
  info: {
    singularName: 'product-pdps-nv';
    pluralName: 'product-pdp-nv';
    displayName: 'PDP de Produto';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    showcase: Attribute.Component<'showcase-list.show-case-list'>;
    packaging: Attribute.Component<'packaging.packaging'>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::product-pdps-nv.product-pdps-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::product-pdps-nv.product-pdps-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiProductPdpsOffpremiumProductPdpsOffpremium
  extends Schema.CollectionType {
  collectionName: 'product_pdp_offpremium';
  info: {
    singularName: 'product-pdps-offpremium';
    pluralName: 'product-pdp-offpremium';
    displayName: 'PDP de produto';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    spot_product: Attribute.Component<'spot-product.spot-product'>;
    highlight: Attribute.Component<'highlight.highlight-list'>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::product-pdps-offpremium.product-pdps-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::product-pdps-offpremium.product-pdps-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPromotionsAnimalePromotionsAnimale
  extends Schema.CollectionType {
  collectionName: 'promotion_animale';
  info: {
    singularName: 'promotions-animale';
    pluralName: 'promotion-animale';
    displayName: 'Promo\u00E7\u00F5es';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['promotion.promotion-card']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::promotions-animale.promotions-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::promotions-animale.promotions-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPromotionsCrisbarrosPromotionsCrisbarros
  extends Schema.CollectionType {
  collectionName: 'promotion_crisbarros';
  info: {
    singularName: 'promotions-crisbarros';
    pluralName: 'promotion-crisbarros';
    displayName: 'Promo\u00E7\u00F5es';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<['promotion.promotion-card']>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::promotions-crisbarros.promotions-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::promotions-crisbarros.promotions-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPromotionsFarmPromotionsFarm extends Schema.CollectionType {
  collectionName: 'promotion_farm';
  info: {
    singularName: 'promotions-farm';
    pluralName: 'promotion-farm';
    displayName: 'Promo\u00E7\u00F5es';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['promotion.promotion-card']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::promotions-farm.promotions-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::promotions-farm.promotions-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPromotionsHeringPromotionsHering
  extends Schema.CollectionType {
  collectionName: 'promotion_hering';
  info: {
    singularName: 'promotions-hering';
    pluralName: 'promotion-hering';
    displayName: 'Promo\u00E7\u00F5es';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['promotion.promotion-card']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::promotions-hering.promotions-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::promotions-hering.promotions-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPromotionsMariafiloPromotionsMariafilo
  extends Schema.CollectionType {
  collectionName: 'promotion_mariafilo';
  info: {
    singularName: 'promotions-mariafilo';
    pluralName: 'promotion-mariafilo';
    displayName: 'Promo\u00E7\u00F5es';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['promotion.promotion-card']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::promotions-mariafilo.promotions-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::promotions-mariafilo.promotions-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPromotionsNvPromotionsNv extends Schema.CollectionType {
  collectionName: 'promotion_nv';
  info: {
    singularName: 'promotions-nv';
    pluralName: 'promotion-nv';
    displayName: 'Promo\u00E7\u00F5es';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['promotion.promotion-card']> &
      Attribute.Required;
    name: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::promotions-nv.promotions-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::promotions-nv.promotions-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPromotionsOffpremiumPromotionsOffpremium
  extends Schema.CollectionType {
  collectionName: 'promotion_offpremium';
  info: {
    singularName: 'promotions-offpremium';
    pluralName: 'promotion-offpremium';
    displayName: 'Promo\u00E7\u00F5es';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['promotion.promotion-card']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::promotions-offpremium.promotions-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::promotions-offpremium.promotions-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiQuickFiltersHeringQuickFiltersHering
  extends Schema.CollectionType {
  collectionName: 'quick_filter_hering';
  info: {
    singularName: 'quick-filters-hering';
    pluralName: 'quick-filter-hering';
    displayName: 'Filtros R\u00E1pidos';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    filter_category_or_cluster: Attribute.String & Attribute.Required;
    filters: Attribute.DynamicZone<['quick-filter.quick-filter-component']>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::quick-filters-hering.quick-filters-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::quick-filters-hering.quick-filters-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSearchTrendsAnimaleSearchTrendsAnimale
  extends Schema.CollectionType {
  collectionName: 'search_trend_animale';
  info: {
    singularName: 'search-trends-animale';
    pluralName: 'search-trend-animale';
    displayName: 'Trends de Busca';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['search-trend.search-trend']>;
    name: Attribute.String &
      Attribute.Required &
      Attribute.DefaultTo<'search_trend'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::search-trends-animale.search-trends-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::search-trends-animale.search-trends-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSearchTrendsFarmSearchTrendsFarm
  extends Schema.CollectionType {
  collectionName: 'search_trend_farm';
  info: {
    singularName: 'search-trends-farm';
    pluralName: 'search-trend-farm';
    displayName: 'Trends de Busca';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['search-trend.search-trend']>;
    name: Attribute.String &
      Attribute.Required &
      Attribute.DefaultTo<'search_trend'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::search-trends-farm.search-trends-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::search-trends-farm.search-trends-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSearchTrendsFarmEtcSearchTrendsFarmEtc
  extends Schema.CollectionType {
  collectionName: 'search_trend_farm_etc';
  info: {
    singularName: 'search-trends-farm-etc';
    pluralName: 'search-trend-farm-etc';
    displayName: 'Trends de Busca ETC';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    Name: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<['search-trend.search-trend']>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::search-trends-farm-etc.search-trends-farm-etc',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::search-trends-farm-etc.search-trends-farm-etc',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSearchTrendsHeringSearchTrendsHering
  extends Schema.CollectionType {
  collectionName: 'search_trend_hering';
  info: {
    singularName: 'search-trends-hering';
    pluralName: 'search-trend-hering';
    displayName: 'Trends de Busca';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['search-trend.search-trend']>;
    name: Attribute.String &
      Attribute.Required &
      Attribute.DefaultTo<'search_trend'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::search-trends-hering.search-trends-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::search-trends-hering.search-trends-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSearchTrendsMariafiloSearchTrendsMariafilo
  extends Schema.CollectionType {
  collectionName: 'search_trend_mariafilo';
  info: {
    singularName: 'search-trends-mariafilo';
    pluralName: 'search-trend-mariafilo';
    displayName: 'Trends de Busca';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['search-trend.search-trend']>;
    name: Attribute.String &
      Attribute.Required &
      Attribute.DefaultTo<'search_trend'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::search-trends-mariafilo.search-trends-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::search-trends-mariafilo.search-trends-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSearchTrendsNvSearchTrendsNv extends Schema.CollectionType {
  collectionName: 'search_trend_nv';
  info: {
    singularName: 'search-trends-nv';
    pluralName: 'search-trend-nv';
    displayName: 'Trends de Busca';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['search-trend.search-trend']>;
    name: Attribute.String &
      Attribute.Required &
      Attribute.DefaultTo<'search_trend'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::search-trends-nv.search-trends-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::search-trends-nv.search-trends-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSearchTrendsOffpremiumSearchTrendsOffpremium
  extends Schema.CollectionType {
  collectionName: 'search_trend_offpremium';
  info: {
    singularName: 'search-trends-offpremium';
    pluralName: 'search-trend-offpremium';
    displayName: 'Trends de Busca';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['search-trend.search-trend']>;
    name: Attribute.String &
      Attribute.Required &
      Attribute.DefaultTo<'search_trend'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::search-trends-offpremium.search-trends-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::search-trends-offpremium.search-trends-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSuggestedSuitcasesHeringSuggestedSuitcasesHering
  extends Schema.CollectionType {
  collectionName: 'suggested_suitcase_hering';
  info: {
    singularName: 'suggested-suitcases-hering';
    pluralName: 'suggested-suitcase-hering';
    displayName: 'Suggested Suitcase';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['showcase-list.show-case-list', 'category-banner.category-banner']
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::suggested-suitcases-hering.suggested-suitcases-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::suggested-suitcases-hering.suggested-suitcases-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSuggestionsFarmSuggestionsFarm
  extends Schema.CollectionType {
  collectionName: 'suggestion_farm';
  info: {
    singularName: 'suggestions-farm';
    pluralName: 'suggestion-farm';
    displayName: 'Sugest\u00F5es';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<
      ['suggestions.cards', 'suggestions.suggestions-title']
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::suggestions-farm.suggestions-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::suggestions-farm.suggestions-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTagsAnimaleTagsAnimale extends Schema.CollectionType {
  collectionName: 'tag_animale';
  info: {
    singularName: 'tags-animale';
    pluralName: 'tag-animale';
    displayName: 'Tags em Geral';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'product-stamp.stamp',
        'tags.product-tag',
        'tags.pix-tag',
        'tags.pickup-tag',
        'tags.pix-immediate-tag'
      ]
    > &
      Attribute.Required;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tags-animale.tags-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tags-animale.tags-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTagsFarmTagsFarm extends Schema.CollectionType {
  collectionName: 'tag_farm';
  info: {
    singularName: 'tags-farm';
    pluralName: 'tag-farm';
    displayName: 'Tags em Geral';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'product-stamp.stamp',
        'tags.product-tag',
        'tags.pix-tag',
        'tags.pickup-tag',
        'tags.pix-immediate-tag'
      ]
    > &
      Attribute.Required;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tags-farm.tags-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tags-farm.tags-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTagsFarmEtcTagsFarmEtc extends Schema.CollectionType {
  collectionName: 'tag_farm_etc';
  info: {
    singularName: 'tags-farm-etc';
    pluralName: 'tag-farm-etc';
    displayName: 'Tags em Geral';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    components: Attribute.DynamicZone<
      [
        'tags.product-tag',
        'tags.stamp-tag-product',
        'tags.stamp-tag',
        'product-stamp.stamp'
      ]
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tags-farm-etc.tags-farm-etc',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tags-farm-etc.tags-farm-etc',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTagsHeringTagsHering extends Schema.CollectionType {
  collectionName: 'tag_hering';
  info: {
    singularName: 'tags-hering';
    pluralName: 'tag-hering';
    displayName: 'Tags em Geral';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'tags.product-tag',
        'product-stamp.stamp',
        'tags.pix-tag',
        'tags.pickup-tag',
        'tags.pix-immediate-tag',
        'tags.product-color-tag'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tags-hering.tags-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tags-hering.tags-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTagsMariafiloTagsMariafilo extends Schema.CollectionType {
  collectionName: 'tag_mariafilo';
  info: {
    singularName: 'tags-mariafilo';
    pluralName: 'tag-mariafilo';
    displayName: 'Tags em Geral';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'product-stamp.stamp',
        'tags.pickup-tag',
        'tags.pix-tag',
        'tags.product-tag',
        'tags.pix-immediate-tag'
      ]
    > &
      Attribute.Required;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tags-mariafilo.tags-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tags-mariafilo.tags-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTagsNvTagsNv extends Schema.CollectionType {
  collectionName: 'tag_nv';
  info: {
    singularName: 'tags-nv';
    pluralName: 'tag-nv';
    displayName: 'Tags em Geral';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'tags.product-tag',
        'tags.fidelity-tags',
        'tags.pix-tag',
        'tags.pix-immediate-tag'
      ]
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tags-nv.tags-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tags-nv.tags-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTagsOffpremiumTagsOffpremium extends Schema.CollectionType {
  collectionName: 'tag_offpremium';
  info: {
    singularName: 'tags-offpremium';
    pluralName: 'tag-offpremium';
    displayName: 'Tags em Geral';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      [
        'product-stamp.stamp',
        'tags.pickup-tag',
        'tags.pix-tag',
        'tags.product-tag',
        'tags.pix-immediate-tag'
      ]
    > &
      Attribute.Required;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tags-offpremium.tags-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tags-offpremium.tags-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTapumesAnimaleTapumesAnimale extends Schema.CollectionType {
  collectionName: 'tapume_animale';
  info: {
    singularName: 'tapumes-animale';
    pluralName: 'tapume-animale';
    displayName: 'Tapume';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    active: Attribute.Boolean & Attribute.Required & Attribute.DefaultTo<false>;
    url_redirect: Attribute.String & Attribute.Required;
    image: Attribute.Media & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tapumes-animale.tapumes-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tapumes-animale.tapumes-animale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTapumesCrisbarrosTapumesCrisbarros
  extends Schema.CollectionType {
  collectionName: 'tapume_crisbarros';
  info: {
    singularName: 'tapumes-crisbarros';
    pluralName: 'tapume-crisbarros';
    displayName: 'Tapume';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    active: Attribute.Boolean & Attribute.Required;
    url_redirect: Attribute.String & Attribute.Required;
    image: Attribute.Media & Attribute.Required;
    start_time: Attribute.DateTime;
    end_time: Attribute.DateTime;
    is_countdown: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
    theme: Attribute.Enumeration<['dark', 'light']> &
      Attribute.Required &
      Attribute.DefaultTo<'dark'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tapumes-crisbarros.tapumes-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tapumes-crisbarros.tapumes-crisbarros',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTapumesFarmTapumesFarm extends Schema.CollectionType {
  collectionName: 'tapume_farm';
  info: {
    singularName: 'tapumes-farm';
    pluralName: 'tapume-farm';
    displayName: 'Tapume';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    active: Attribute.Boolean & Attribute.Required & Attribute.DefaultTo<false>;
    url_redirect: Attribute.String & Attribute.Required;
    image: Attribute.Media & Attribute.Required;
    max_discount: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tapumes-farm.tapumes-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tapumes-farm.tapumes-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTapumesFarmEtcTapumesFarmEtc extends Schema.CollectionType {
  collectionName: 'tapume_farm_etc';
  info: {
    singularName: 'tapumes-farm-etc';
    pluralName: 'tapume-farm-etc';
    displayName: 'Tapume ETC';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    active: Attribute.Boolean;
    image: Attribute.Media;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tapumes-farm-etc.tapumes-farm-etc',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tapumes-farm-etc.tapumes-farm-etc',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTapumesHeringTapumesHering extends Schema.CollectionType {
  collectionName: 'tapume_hering';
  info: {
    singularName: 'tapumes-hering';
    pluralName: 'tapume-hering';
    displayName: 'Tapume';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    url_redirect: Attribute.String & Attribute.Required;
    active: Attribute.Boolean & Attribute.Required & Attribute.DefaultTo<false>;
    image: Attribute.Media & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tapumes-hering.tapumes-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tapumes-hering.tapumes-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTapumesMariafiloTapumesMariafilo
  extends Schema.CollectionType {
  collectionName: 'tapume_mariafilo';
  info: {
    singularName: 'tapumes-mariafilo';
    pluralName: 'tapume-mariafilo';
    displayName: 'Tapume';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    active: Attribute.Boolean & Attribute.Required & Attribute.DefaultTo<false>;
    url_redirect: Attribute.String & Attribute.Required;
    image: Attribute.Media & Attribute.Required;
    start_time: Attribute.DateTime;
    end_time: Attribute.DateTime;
    is_countdown: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
    theme: Attribute.Enumeration<['dark', 'light']>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tapumes-mariafilo.tapumes-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tapumes-mariafilo.tapumes-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTapumesNvTapumesNv extends Schema.CollectionType {
  collectionName: 'tapume_nv';
  info: {
    singularName: 'tapumes-nv';
    pluralName: 'tapume-nv';
    displayName: 'Tapume';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    active: Attribute.Boolean & Attribute.Required & Attribute.DefaultTo<false>;
    image: Attribute.Media & Attribute.Required;
    url_redirect: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tapumes-nv.tapumes-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tapumes-nv.tapumes-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTapumesOffpremiumTapumesOffpremium
  extends Schema.CollectionType {
  collectionName: 'tapume_offpremium';
  info: {
    singularName: 'tapumes-offpremium';
    pluralName: 'tapume-offpremium';
    displayName: 'Tapume';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    active: Attribute.Boolean & Attribute.Required & Attribute.DefaultTo<false>;
    url_redirect: Attribute.String & Attribute.Required;
    image: Attribute.Media & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tapumes-offpremium.tapumes-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tapumes-offpremium.tapumes-offpremium',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTipBarsFarmTipBarsFarm extends Schema.CollectionType {
  collectionName: 'tip_bar_farm';
  info: {
    singularName: 'tip-bars-farm';
    pluralName: 'tip-bar-farm';
    displayName: 'TipBar';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['tip-bar.static-tip-bar', 'tip-bar.animated-tip-bar']
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tip-bars-farm.tip-bars-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tip-bars-farm.tip-bars-farm',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTipBarsHeringTipBarsHering extends Schema.CollectionType {
  collectionName: 'tip_bar_hering';
  info: {
    singularName: 'tip-bars-hering';
    pluralName: 'tip-bar-hering';
    displayName: 'TipBar';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String;
    components: Attribute.DynamicZone<
      ['tip-bar.animated-tip-bar', 'tip-bar.static-tip-bar']
    >;
    filter_category_or_cluster: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tip-bars-hering.tip-bars-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tip-bars-hering.tip-bars-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTipBarsMariafiloTipBarsMariafilo
  extends Schema.CollectionType {
  collectionName: 'tip_bar_mariafilo';
  info: {
    singularName: 'tip-bars-mariafilo';
    pluralName: 'tip-bar-mariafilo';
    displayName: 'TipBar';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['tip-bar.static-tip-bar', 'tip-bar.animated-tip-bar']
    >;
    name: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tip-bars-mariafilo.tip-bars-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tip-bars-mariafilo.tip-bars-mariafilo',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTipBarsNvTipBarsNv extends Schema.CollectionType {
  collectionName: 'tip_bar_nv';
  info: {
    singularName: 'tip-bars-nv';
    pluralName: 'tip-bar-nv';
    displayName: 'TipBar';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['tip-bar.static-tip-bar']>;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tip-bars-nv.tip-bars-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tip-bars-nv.tip-bars-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTravelSuitcasesHeringTravelSuitcasesHering
  extends Schema.CollectionType {
  collectionName: 'travel_suitcase_hering';
  info: {
    singularName: 'travel-suitcases-hering';
    pluralName: 'travel-suitcase-hering';
    displayName: 'Mala de Viagem';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<
      ['suitcase-suggestion.suitcase-suggestion']
    >;
    name: Attribute.String & Attribute.Required & Attribute.Private;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::travel-suitcases-hering.travel-suitcases-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::travel-suitcases-hering.travel-suitcases-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTurbosHeringTurbosHering extends Schema.CollectionType {
  collectionName: 'turbo_hering';
  info: {
    singularName: 'turbos-hering';
    pluralName: 'turbo-hering';
    displayName: 'Turbo';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    active: Attribute.Boolean & Attribute.Required & Attribute.DefaultTo<false>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::turbos-hering.turbos-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::turbos-hering.turbos-hering',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiUnloggedsNotificationsCentersNvUnloggedsNotificationsCentersNv
  extends Schema.CollectionType {
  collectionName: 'unloggeds_notifications_center_nv';
  info: {
    singularName: 'unloggeds-notifications-centers-nv';
    pluralName: 'unlogged-notification-center-nv';
    displayName: 'Central de Notifica\u00E7\u00F5es - Deslogado';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    components: Attribute.DynamicZone<['sign-up-login.sign-up-login']> &
      Attribute.Required;
    name: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::unloggeds-notifications-centers-nv.unloggeds-notifications-centers-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::unloggeds-notifications-centers-nv.unloggeds-notifications-centers-nv',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface ContentTypes {
      'admin::permission': AdminPermission;
      'admin::user': AdminUser;
      'admin::role': AdminRole;
      'admin::api-token': AdminApiToken;
      'admin::api-token-permission': AdminApiTokenPermission;
      'admin::transfer-token': AdminTransferToken;
      'admin::transfer-token-permission': AdminTransferTokenPermission;
      'plugin::upload.file': PluginUploadFile;
      'plugin::upload.folder': PluginUploadFolder;
      'plugin::content-releases.release': PluginContentReleasesRelease;
      'plugin::content-releases.release-action': PluginContentReleasesReleaseAction;
      'plugin::users-permissions.permission': PluginUsersPermissionsPermission;
      'plugin::users-permissions.role': PluginUsersPermissionsRole;
      'plugin::users-permissions.user': PluginUsersPermissionsUser;
      'plugin::publisher.action': PluginPublisherAction;
      'plugin::i18n.locale': PluginI18NLocale;
      'api::ai-virtual-assistants-hering.ai-virtual-assistants-hering': ApiAiVirtualAssistantsHeringAiVirtualAssistantsHering;
      'api::bags-animale.bags-animale': ApiBagsAnimaleBagsAnimale;
      'api::bags-crisbarros.bags-crisbarros': ApiBagsCrisbarrosBagsCrisbarros;
      'api::bags-farm.bags-farm': ApiBagsFarmBagsFarm;
      'api::bags-hering.bags-hering': ApiBagsHeringBagsHering;
      'api::bags-mariafilo.bags-mariafilo': ApiBagsMariafiloBagsMariafilo;
      'api::bags-nv.bags-nv': ApiBagsNvBagsNv;
      'api::bags-offpremium.bags-offpremium': ApiBagsOffpremiumBagsOffpremium;
      'api::call-centers-hering.call-centers-hering': ApiCallCentersHeringCallCentersHering;
      'api::clock-promotion-animales.clock-promotion-animales': ApiClockPromotionAnimalesClockPromotionAnimales;
      'api::clock-promotion-farms.clock-promotion-farms': ApiClockPromotionFarmsClockPromotionFarms;
      'api::clock-promotion-herings.clock-promotion-herings': ApiClockPromotionHeringsClockPromotionHerings;
      'api::clock-promotion-mariafilos.clock-promotion-mariafilos': ApiClockPromotionMariafilosClockPromotionMariafilos;
      'api::clock-promotion-nvs.clock-promotion-nvs': ApiClockPromotionNvsClockPromotionNvs;
      'api::clock-promotion-offpremiums.clock-promotion-offpremiums': ApiClockPromotionOffpremiumsClockPromotionOffpremiums;
      'api::combo-de-promocoess-hering.combo-de-promocoess-hering': ApiComboDePromocoessHeringComboDePromocoessHering;
      'api::coupons-animale.coupons-animale': ApiCouponsAnimaleCouponsAnimale;
      'api::created-suitcases-hering.created-suitcases-hering': ApiCreatedSuitcasesHeringCreatedSuitcasesHering;
      'api::deeplinks-animale.deeplinks-animale': ApiDeeplinksAnimaleDeeplinksAnimale;
      'api::deeplinks-crisbarros.deeplinks-crisbarros': ApiDeeplinksCrisbarrosDeeplinksCrisbarros;
      'api::deeplinks-farm.deeplinks-farm': ApiDeeplinksFarmDeeplinksFarm;
      'api::deeplinks-hering.deeplinks-hering': ApiDeeplinksHeringDeeplinksHering;
      'api::deeplinks-mariafilo.deeplinks-mariafilo': ApiDeeplinksMariafiloDeeplinksMariafilo;
      'api::deeplinks-nv.deeplinks-nv': ApiDeeplinksNvDeeplinksNv;
      'api::deeplinks-offpremium.deeplinks-offpremium': ApiDeeplinksOffpremiumDeeplinksOffpremium;
      'api::empty-bags-animale.empty-bags-animale': ApiEmptyBagsAnimaleEmptyBagsAnimale;
      'api::empty-bags-crisbarros.empty-bags-crisbarros': ApiEmptyBagsCrisbarrosEmptyBagsCrisbarros;
      'api::empty-bags-farm.empty-bags-farm': ApiEmptyBagsFarmEmptyBagsFarm;
      'api::empty-bags-hering.empty-bags-hering': ApiEmptyBagsHeringEmptyBagsHering;
      'api::empty-bags-mariafilo.empty-bags-mariafilo': ApiEmptyBagsMariafiloEmptyBagsMariafilo;
      'api::empty-bags-nv.empty-bags-nv': ApiEmptyBagsNvEmptyBagsNv;
      'api::empty-bags-offpremium.empty-bags-offpremium': ApiEmptyBagsOffpremiumEmptyBagsOffpremium;
      'api::empty-wishlists-animale.empty-wishlists-animale': ApiEmptyWishlistsAnimaleEmptyWishlistsAnimale;
      'api::empty-wishlists-crisbarros.empty-wishlists-crisbarros': ApiEmptyWishlistsCrisbarrosEmptyWishlistsCrisbarros;
      'api::empty-wishlists-hering.empty-wishlists-hering': ApiEmptyWishlistsHeringEmptyWishlistsHering;
      'api::empty-wishlists-mariafilo.empty-wishlists-mariafilo': ApiEmptyWishlistsMariafiloEmptyWishlistsMariafilo;
      'api::empty-wishlists-nv.empty-wishlists-nv': ApiEmptyWishlistsNvEmptyWishlistsNv;
      'api::empty-wishlists-offpremium.empty-wishlists-offpremium': ApiEmptyWishlistsOffpremiumEmptyWishlistsOffpremium;
      'api::emptys-notifications-centers-nv.emptys-notifications-centers-nv': ApiEmptysNotificationsCentersNvEmptysNotificationsCentersNv;
      'api::emptys-searchs-nv.emptys-searchs-nv': ApiEmptysSearchsNvEmptysSearchsNv;
      'api::fidelities-nv.fidelities-nv': ApiFidelitiesNvFidelitiesNv;
      'api::full-look-pages-farms.full-look-pages-farms': ApiFullLookPagesFarmsFullLookPagesFarms;
      'api::full-look-pages-herings.full-look-pages-herings': ApiFullLookPagesHeringsFullLookPagesHerings;
      'api::general-header-pdc-etcs-farm.general-header-pdc-etcs-farm': ApiGeneralHeaderPdcEtcsFarmGeneralHeaderPdcEtcsFarm;
      'api::homes-animale.homes-animale': ApiHomesAnimaleHomesAnimale;
      'api::homes-crisbarros.homes-crisbarros': ApiHomesCrisbarrosHomesCrisbarros;
      'api::homes-etc.homes-etc': ApiHomesEtcHomesEtc;
      'api::homes-farm.homes-farm': ApiHomesFarmHomesFarm;
      'api::homes-hering.homes-hering': ApiHomesHeringHomesHering;
      'api::homes-mariafilo.homes-mariafilo': ApiHomesMariafiloHomesMariafilo;
      'api::homes-nv.homes-nv': ApiHomesNvHomesNv;
      'api::homes-offpremium.homes-offpremium': ApiHomesOffpremiumHomesOffpremium;
      'api::hrg-homes-hering.hrg-homes-hering': ApiHrgHomesHeringHrgHomesHering;
      'api::live-transmissions-mariafilo.live-transmissions-mariafilo': ApiLiveTransmissionsMariafiloLiveTransmissionsMariafilo;
      'api::login-backgrounds-crisbarros.login-backgrounds-crisbarros': ApiLoginBackgroundsCrisbarrosLoginBackgroundsCrisbarros;
      'api::login-backgrounds-farm.login-backgrounds-farm': ApiLoginBackgroundsFarmLoginBackgroundsFarm;
      'api::media-kits-etc-farm.media-kits-etc-farm': ApiMediaKitsEtcFarmMediaKitsEtcFarm;
      'api::media-kits-farm.media-kits-farm': ApiMediaKitsFarmMediaKitsFarm;
      'api::media-kits-hering.media-kits-hering': ApiMediaKitsHeringMediaKitsHering;
      'api::media-kits-mariafilo.media-kits-mariafilo': ApiMediaKitsMariafiloMediaKitsMariafilo;
      'api::media-kits-nv.media-kits-nv': ApiMediaKitsNvMediaKitsNv;
      'api::menu-collections-farm.menu-collections-farm': ApiMenuCollectionsFarmMenuCollectionsFarm;
      'api::menu-collections-farm-etc.menu-collections-farm-etc': ApiMenuCollectionsFarmEtcMenuCollectionsFarmEtc;
      'api::menu-contents-animale.menu-contents-animale': ApiMenuContentsAnimaleMenuContentsAnimale;
      'api::menu-contents-crisbarros.menu-contents-crisbarros': ApiMenuContentsCrisbarrosMenuContentsCrisbarros;
      'api::menu-contents-farm.menu-contents-farm': ApiMenuContentsFarmMenuContentsFarm;
      'api::menu-contents-farm-etc.menu-contents-farm-etc': ApiMenuContentsFarmEtcMenuContentsFarmEtc;
      'api::menu-contents-hering.menu-contents-hering': ApiMenuContentsHeringMenuContentsHering;
      'api::menu-contents-mariafilo.menu-contents-mariafilo': ApiMenuContentsMariafiloMenuContentsMariafilo;
      'api::menu-contents-nv.menu-contents-nv': ApiMenuContentsNvMenuContentsNv;
      'api::menu-contents-offpremium.menu-contents-offpremium': ApiMenuContentsOffpremiumMenuContentsOffpremium;
      'api::menus-animale.menus-animale': ApiMenusAnimaleMenusAnimale;
      'api::menus-crisbarros.menus-crisbarros': ApiMenusCrisbarrosMenusCrisbarros;
      'api::menus-farm.menus-farm': ApiMenusFarmMenusFarm;
      'api::menus-farm-etc.menus-farm-etc': ApiMenusFarmEtcMenusFarmEtc;
      'api::menus-hering.menus-hering': ApiMenusHeringMenusHering;
      'api::menus-mariafilo.menus-mariafilo': ApiMenusMariafiloMenusMariafilo;
      'api::menus-nv.menus-nv': ApiMenusNvMenusNv;
      'api::menus-offpremium.menus-offpremium': ApiMenusOffpremiumMenusOffpremium;
      'api::non-vip-feedbacks-nv.non-vip-feedbacks-nv': ApiNonVipFeedbacksNvNonVipFeedbacksNv;
      'api::notifications-centers-animale.notifications-centers-animale': ApiNotificationsCentersAnimaleNotificationsCentersAnimale;
      'api::notifications-centers-farm.notifications-centers-farm': ApiNotificationsCentersFarmNotificationsCentersFarm;
      'api::notifications-centers-hering.notifications-centers-hering': ApiNotificationsCentersHeringNotificationsCentersHering;
      'api::notifications-centers-nv.notifications-centers-nv': ApiNotificationsCentersNvNotificationsCentersNv;
      'api::onboardings-hering.onboardings-hering': ApiOnboardingsHeringOnboardingsHering;
      'api::onboardings-nv.onboardings-nv': ApiOnboardingsNvOnboardingsNv;
      'api::order-placeds-crisbarros.order-placeds-crisbarros': ApiOrderPlacedsCrisbarrosOrderPlacedsCrisbarros;
      'api::orders-details-hering.orders-details-hering': ApiOrdersDetailsHeringOrdersDetailsHering;
      'api::payments-crisbarros.payments-crisbarros': ApiPaymentsCrisbarrosPaymentsCrisbarros;
      'api::pdp-etcs-farm.pdp-etcs-farm': ApiPdpEtcsFarmPdpEtcsFarm;
      'api::product-pdps-animale.product-pdps-animale': ApiProductPdpsAnimaleProductPdpsAnimale;
      'api::product-pdps-crisbarros.product-pdps-crisbarros': ApiProductPdpsCrisbarrosProductPdpsCrisbarros;
      'api::product-pdps-farm.product-pdps-farm': ApiProductPdpsFarmProductPdpsFarm;
      'api::product-pdps-hering.product-pdps-hering': ApiProductPdpsHeringProductPdpsHering;
      'api::product-pdps-mariafilo.product-pdps-mariafilo': ApiProductPdpsMariafiloProductPdpsMariafilo;
      'api::product-pdps-nv.product-pdps-nv': ApiProductPdpsNvProductPdpsNv;
      'api::product-pdps-offpremium.product-pdps-offpremium': ApiProductPdpsOffpremiumProductPdpsOffpremium;
      'api::promotions-animale.promotions-animale': ApiPromotionsAnimalePromotionsAnimale;
      'api::promotions-crisbarros.promotions-crisbarros': ApiPromotionsCrisbarrosPromotionsCrisbarros;
      'api::promotions-farm.promotions-farm': ApiPromotionsFarmPromotionsFarm;
      'api::promotions-hering.promotions-hering': ApiPromotionsHeringPromotionsHering;
      'api::promotions-mariafilo.promotions-mariafilo': ApiPromotionsMariafiloPromotionsMariafilo;
      'api::promotions-nv.promotions-nv': ApiPromotionsNvPromotionsNv;
      'api::promotions-offpremium.promotions-offpremium': ApiPromotionsOffpremiumPromotionsOffpremium;
      'api::quick-filters-hering.quick-filters-hering': ApiQuickFiltersHeringQuickFiltersHering;
      'api::search-trends-animale.search-trends-animale': ApiSearchTrendsAnimaleSearchTrendsAnimale;
      'api::search-trends-farm.search-trends-farm': ApiSearchTrendsFarmSearchTrendsFarm;
      'api::search-trends-farm-etc.search-trends-farm-etc': ApiSearchTrendsFarmEtcSearchTrendsFarmEtc;
      'api::search-trends-hering.search-trends-hering': ApiSearchTrendsHeringSearchTrendsHering;
      'api::search-trends-mariafilo.search-trends-mariafilo': ApiSearchTrendsMariafiloSearchTrendsMariafilo;
      'api::search-trends-nv.search-trends-nv': ApiSearchTrendsNvSearchTrendsNv;
      'api::search-trends-offpremium.search-trends-offpremium': ApiSearchTrendsOffpremiumSearchTrendsOffpremium;
      'api::suggested-suitcases-hering.suggested-suitcases-hering': ApiSuggestedSuitcasesHeringSuggestedSuitcasesHering;
      'api::suggestions-farm.suggestions-farm': ApiSuggestionsFarmSuggestionsFarm;
      'api::tags-animale.tags-animale': ApiTagsAnimaleTagsAnimale;
      'api::tags-farm.tags-farm': ApiTagsFarmTagsFarm;
      'api::tags-farm-etc.tags-farm-etc': ApiTagsFarmEtcTagsFarmEtc;
      'api::tags-hering.tags-hering': ApiTagsHeringTagsHering;
      'api::tags-mariafilo.tags-mariafilo': ApiTagsMariafiloTagsMariafilo;
      'api::tags-nv.tags-nv': ApiTagsNvTagsNv;
      'api::tags-offpremium.tags-offpremium': ApiTagsOffpremiumTagsOffpremium;
      'api::tapumes-animale.tapumes-animale': ApiTapumesAnimaleTapumesAnimale;
      'api::tapumes-crisbarros.tapumes-crisbarros': ApiTapumesCrisbarrosTapumesCrisbarros;
      'api::tapumes-farm.tapumes-farm': ApiTapumesFarmTapumesFarm;
      'api::tapumes-farm-etc.tapumes-farm-etc': ApiTapumesFarmEtcTapumesFarmEtc;
      'api::tapumes-hering.tapumes-hering': ApiTapumesHeringTapumesHering;
      'api::tapumes-mariafilo.tapumes-mariafilo': ApiTapumesMariafiloTapumesMariafilo;
      'api::tapumes-nv.tapumes-nv': ApiTapumesNvTapumesNv;
      'api::tapumes-offpremium.tapumes-offpremium': ApiTapumesOffpremiumTapumesOffpremium;
      'api::tip-bars-farm.tip-bars-farm': ApiTipBarsFarmTipBarsFarm;
      'api::tip-bars-hering.tip-bars-hering': ApiTipBarsHeringTipBarsHering;
      'api::tip-bars-mariafilo.tip-bars-mariafilo': ApiTipBarsMariafiloTipBarsMariafilo;
      'api::tip-bars-nv.tip-bars-nv': ApiTipBarsNvTipBarsNv;
      'api::travel-suitcases-hering.travel-suitcases-hering': ApiTravelSuitcasesHeringTravelSuitcasesHering;
      'api::turbos-hering.turbos-hering': ApiTurbosHeringTurbosHering;
      'api::unloggeds-notifications-centers-nv.unloggeds-notifications-centers-nv': ApiUnloggedsNotificationsCentersNvUnloggedsNotificationsCentersNv;
    }
  }
}
